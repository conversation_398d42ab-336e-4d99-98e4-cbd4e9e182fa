/**
 * AI 云对象主模块
 *
 * 提供智能任务执行和流式聊天功能：
 * - 任务意图识别和执行计划生成
 * - 多步骤任务的自动化执行
 * - 实时流式聊天响应
 * - Todo 工具集成和测试功能
 * - 执行上下文管理和数据传递
 */

/**
 * 执行上下文管理器
 * 用于管理任务执行过程中的所有数据和状态信息
 *
 * 数据结构：
 * - stepResults: Map<stepId, {result, metadata}> 存储步骤执行结果
 * - contextData: Map<key, value> 存储提取的上下文数据
 * - metadata: Object 存储执行过程的元数据信息
 *
 * 核心功能：
 * - 步骤结果管理：存储和检索每个步骤的执行结果
 * - 上下文数据提取：根据 AI 提取的实体，从结果中提取关键信息
 * - 项目智能匹配：基于 AI 提取的项目名称，匹配最相关的项目
 */
const { OpenAI } = require('openai')
const {
  FUNCTION_TOOLS,
  doubaoParams,
  SSE_MESSAGE_TYPES,
  createSSEMessage,
  generateSessionId,
} = require('./modules/config.js')

/**
 * 流式工具调用处理（豆包模型优化版）
 */
async function handleStreamResponse(streamResponse, sseChannel, sessionId, originalMessages) {
  let pendingToolCalls = [] // 使用数组存储工具调用，支持索引
  let assistantMessage = ''
  let hasToolCalls = false

  for await (const chunk of streamResponse) {
    const delta = chunk.choices[0]?.delta
    const finishReason = chunk.choices[0]?.finish_reason

    // 处理普通文本内容
    if (delta?.content) {
      assistantMessage += delta.content
      await sseChannel.write(
        createSSEMessage('CHAT_CONTENT_CHUNK', sessionId, {
          content: delta.content,
          isComplete: false,
        })
      )
    }

    // 处理工具调用 - 豆包模型增量式处理
    if (delta?.tool_calls) {
      hasToolCalls = true

      for (const toolCallDelta of delta.tool_calls) {
        const index = toolCallDelta.index || 0
        const toolCallId = toolCallDelta.id

        // 初始化工具调用对象
        if (!pendingToolCalls[index]) {
          pendingToolCalls[index] = {
            id: toolCallId || `call_${Date.now()}_${index}`,
            type: 'function',
            function: {
              name: toolCallDelta.function?.name || '',
              arguments: toolCallDelta.function?.arguments || '',
            },
          }

          // 推送工具调用开始消息
          if (toolCallDelta.function?.name) {
            await sseChannel.write(
              createSSEMessage('TOOL_CALL_START', sessionId, {
                toolName: toolCallDelta.function.name,
                toolCallId: pendingToolCalls[index].id,
              })
            )
          }
        } else {
          // 累积工具调用参数
          if (toolCallDelta.function?.name) {
            pendingToolCalls[index].function.name = toolCallDelta.function.name
          }
          if (toolCallDelta.function?.arguments) {
            pendingToolCalls[index].function.arguments += toolCallDelta.function.arguments
          }
        }
      }
    }

    // 检查是否完成工具调用
    if (finishReason === 'tool_calls' && hasToolCalls) {
      // 执行所有完整的工具调用
      const toolResults = []

      for (const toolCall of pendingToolCalls.filter((tc) => tc && tc.function.name)) {
        try {
          const result = await executeToolCall(toolCall, sseChannel, sessionId)
          toolResults.push({
            toolCall: toolCall,
            result: result,
          })
        } catch (error) {
          console.error('工具调用失败：', error)
          await sseChannel.write(
            createSSEMessage('TOOL_EXECUTION_ERROR', sessionId, {
              toolName: toolCall.function.name,
              error: error.message,
            })
          )

          // 即使失败也要记录，以便后续处理
          toolResults.push({
            toolCall: toolCall,
            result: { error: error.message, success: false },
          })
        }
      }

      // 继续对话，让模型基于工具结果生成最终回复
      if (toolResults.length > 0) {
        await continueConversationWithToolResults(
          originalMessages,
          pendingToolCalls.filter((tc) => tc),
          toolResults,
          sseChannel,
          sessionId
        )
      }
    }

    // 处理普通对话结束
    if (finishReason === 'stop' && !hasToolCalls) {
      await sseChannel.write(
        createSSEMessage('CHAT_CONTENT_CHUNK', sessionId, {
          content: '',
          isComplete: true,
        })
      )
    }
  }
}

/**
 * 继续对话 - 将工具调用结果传回模型
 */
async function continueConversationWithToolResults(originalMessages, toolCalls, toolResults, sseChannel, sessionId) {
  const openai = new OpenAI(doubaoParams)

  // 构建包含工具调用和结果的完整消息历史
  const messagesWithToolResults = [
    ...originalMessages,
    // 添加助手的工具调用消息
    {
      role: 'assistant',
      content: null,
      tool_calls: toolCalls,
    },
    // 添加工具执行结果消息
    ...toolResults.map(({ toolCall, result }) => ({
      role: 'tool',
      tool_call_id: toolCall.id,
      content: JSON.stringify(result),
    })),
  ]

  // 推送工具结果处理开始消息
  await sseChannel.write(
    createSSEMessage('TOOL_RESULT_PROCESSING', sessionId, {
      message: '正在基于工具执行结果生成回复...',
    })
  )

  try {
    // 重新调用模型，让其基于工具结果生成最终回复
    const followUpResponse = await openai.chat.completions.create({
      model: 'doubao-seed-1-6-flash-250715',
      messages: messagesWithToolResults,
      stream: true,
      timeout: 60000,
      thinking: { type: 'disabled' },
    })

    // 处理后续回复
    for await (const chunk of followUpResponse) {
      const delta = chunk.choices[0]?.delta

      if (delta?.content) {
        await sseChannel.write(
          createSSEMessage('CHAT_CONTENT_CHUNK', sessionId, {
            content: delta.content,
            isComplete: false,
          })
        )
      }

      if (chunk.choices[0]?.finish_reason === 'stop') {
        await sseChannel.write(
          createSSEMessage('CHAT_CONTENT_CHUNK', sessionId, {
            content: '',
            isComplete: true,
          })
        )
      }
    }
  } catch (error) {
    console.error('工具结果处理失败：', error)
    await sseChannel.write(
      createSSEMessage('TOOL_RESULT_ERROR', sessionId, {
        error: '基于工具结果生成回复失败',
        details: error.message,
      })
    )
  }
}

/**
 * 统一工具执行接口
 */
async function executeToolCall(toolCall, sseChannel, sessionId) {
  const { function: func } = toolCall
  const toolName = func.name

  try {
    // 解析工具参数
    const parameters = JSON.parse(func.arguments)

    // 推送工具执行开始消息
    await sseChannel.write(
      createSSEMessage('TOOL_EXECUTION_START', sessionId, {
        toolName: toolName,
        parameters: parameters,
      })
    )

    // 执行具体工具
    let result
    switch (toolName) {
      case 'getTasks':
        result = await executeGetTasks(parameters)
        break
      case 'createTask':
        result = await executeCreateTask(parameters)
        break
      case 'getProjects':
        result = await executeGetProjects(parameters)
        break
      case 'updateTask':
        result = await executeUpdateTask(parameters)
        break
      default:
        throw new Error(`未知的工具：${toolName}`)
    }

    // 推送工具执行完成消息
    await sseChannel.write(
      createSSEMessage('TOOL_EXECUTION_COMPLETE', sessionId, {
        toolName: toolName,
        result: result,
        success: true,
      })
    )

    return result
  } catch (error) {
    // 推送工具执行失败消息
    await sseChannel.write(
      createSSEMessage('TOOL_EXECUTION_ERROR', sessionId, {
        toolName: toolName,
        error: error.message,
        success: false,
      })
    )

    throw error
  }
}

module.exports = {
  async chatStreamSSE({ channel, message, messages: history_records }) {
    const sessionId = generateSessionId()

    // 参数验证
    if (!message) {
      return {
        errCode: 'PARAM_IS_NULL',
        errMsg: '消息内容不能为空',
      }
    }

    if (!channel) {
      return {
        errCode: 'PARAM_IS_NULL',
        errMsg: 'SSE Channel 不能为空',
      }
    }

    try {
      const sseChannel = uniCloud.deserializeSSEChannel(channel)

      // 推送开始处理消息
      await sseChannel.write(
        createSSEMessage('PROCESSING_START', sessionId, {
          message: '开始处理您的请求...',
        })
      )

      // 初始化 AI 客户端
      const openai = new OpenAI(doubaoParams)

      // 构建消息数组 - 正确处理历史消息格式
      const messages = [
        {
          role: 'system',
          content:
            '你是一个专业的任务管理助手。你可以帮助用户管理任务和项目。当用户需要执行具体操作时，请调用相应的工具函数。对于一般性问题，可以直接回答。',
        },
        // 正确处理历史消息，保留工具调用相关信息
        ...history_records.map((msg) => ({
          role: msg.role,
          content: msg.content,
          // 保留工具调用相关信息
          ...(msg.tool_calls && { tool_calls: msg.tool_calls }),
          ...(msg.tool_call_id && { tool_call_id: msg.tool_call_id }),
        })),
        {
          role: 'user',
          content: message,
        },
      ]

      // 创建流式响应
      const streamResponse = await openai.chat.completions.create({
        model: 'doubao-seed-1-6-flash-250715',
        messages: messages,
        tools: FUNCTION_TOOLS,
        tool_choice: 'auto',
        stream: true,
        timeout: 60000,
        thinking: { type: 'disabled' },
      })

      // 处理流式响应 - 传入原始消息用于工具调用后续处理
      await handleStreamResponse(streamResponse, sseChannel, sessionId, messages)

      // 推送会话结束消息
      await sseChannel.end(
        createSSEMessage('SESSION_END', sessionId, {
          message: '处理完成',
        })
      )

      return {
        errCode: 0,
        errMsg: 'success',
        data: {
          type: 'function_calling_complete',
          sessionId: sessionId,
        },
      }
    } catch (error) {
      console.error('chatStreamSSE 错误：', error)

      // 尝试推送错误消息
      try {
        const sseChannel = uniCloud.deserializeSSEChannel(channel)
        await sseChannel.end(
          createSSEMessage('ERROR', sessionId, {
            error: error.message,
            timestamp: new Date().toISOString(),
          })
        )
      } catch (channelError) {
        console.error('SSE 推送错误：', channelError)
      }

      return {
        errCode: 'SYSTEM_ERROR',
        errMsg: error.message || '系统处理失败',
        data: {
          type: 'system_error',
          sessionId: sessionId,
        },
      }
    }
  },
}

// 获取任务列表
async function executeGetTasks(parameters) {
  const didaApi = uniCloud.importObject('dida-api')

  let result
  if (parameters.keyword) {
    // 如果有关键词，使用搜索接口
    result = await didaApi.searchTasks(parameters.keyword, parameters.limit || 20)
  } else {
    // 否则获取所有任务
    result = await didaApi.getAllTasks()
  }

  if (result.errCode !== null && result.errCode !== 0) {
    throw new Error(`获取任务失败：${result.errMsg}`)
  }

  // 处理任务数据，根据参数进行筛选
  let tasks = result.data?.tasks || []

  // 根据完成状态筛选
  if (parameters.completed !== undefined) {
    tasks = tasks.filter((task) => {
      const isCompleted = task.status === 2
      return parameters.completed ? isCompleted : !isCompleted
    })
  }

  // 限制返回数量
  if (parameters.limit) {
    tasks = tasks.slice(0, parameters.limit)
  }

  return {
    success: true,
    data: tasks,
    message: `成功获取 ${tasks.length} 个任务`,
  }
}

// 创建任务 - 暂时返回模拟数据，因为 dida-api 没有创建任务的接口
async function executeCreateTask(parameters) {
  // 注意：dida-api 目前没有创建任务的接口，这里返回模拟响应
  return {
    success: true,
    data: {
      id: `task_${Date.now()}`,
      title: parameters.title,
      content: parameters.content || '',
      status: 0,
      priority: parameters.priority || 0,
      projectId: parameters.projectId || null,
      createdTime: new Date().toISOString(),
    },
    message: `任务创建请求已接收：${parameters.title}（注意：当前为模拟响应，实际创建功能需要完善 dida-api 接口）`,
  }
}

// 获取项目列表
async function executeGetProjects(parameters) {
  const didaApi = uniCloud.importObject('dida-api')

  const result = await didaApi.getProjects({
    keyword: parameters.keyword,
    includeClosed: parameters.includeClosed || false,
  })

  if (result.errCode !== 0) {
    throw new Error(`获取项目失败：${result.errMsg}`)
  }

  return {
    success: true,
    data: result.data,
    message: `成功获取 ${result.data?.length || 0} 个项目`,
  }
}

// 更新任务 - 暂时返回模拟数据，因为 dida-api 没有更新任务的接口
async function executeUpdateTask(parameters) {
  // 注意：dida-api 目前没有更新任务的接口，这里返回模拟响应
  return {
    success: true,
    data: {
      id: parameters.taskId,
      title: parameters.title,
      content: parameters.content,
      status: parameters.completed ? 2 : 0,
      priority: parameters.priority,
      modifiedTime: new Date().toISOString(),
    },
    message: `任务更新请求已接收（注意：当前为模拟响应，实际更新功能需要完善 dida-api 接口）`,
  }
}
